<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Template - Editable Form</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @page {
            margin: 0;
            padding: 0;
            size: A4 portrait;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }

        .container-fluid {
            max-width: 1200px;
            margin: 0 auto;
        }

        .tar-container {
            position: relative;
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .template-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .template-background img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .content-overlay {
            position: relative;
            z-index: 2;
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
        }

        .editable-field {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border: 1px dashed #007bff;
            padding: 2px 4px;
            font-size: 10px;
            font-weight: bold;
            color: #000;
            min-height: 16px;
            cursor: text;
            transition: all 0.3s ease;
        }

        .editable-field:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: #0056b3;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }

        .editable-field:focus {
            outline: none;
            background: rgba(255, 255, 255, 1);
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        }

        .editable-field[contenteditable="true"]:empty:before {
            content: attr(data-placeholder);
            color: #6c757d;
            font-style: italic;
        }

        /* Header Form Fields */
        .header-form {
            position: absolute;
            top: 18.5mm;
            right: 15mm;
        }

        .form-field {
            font-size: 12px;
            margin-bottom: 2mm;
            color: #000;
        }

        /* Data Unit Fields */
        .data-fields {
            position: absolute;
            top: 36mm;
            left: 22mm;
            right: 15mm;
        }

        /* Control Panel */
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 250px;
        }

        .btn-generate {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }

        .loading-spinner {
            display: none;
        }

        /* Part Problems Table */
        .parts-table-container {
            position: absolute;
            top: 110.5mm;
            left: 5mm;
            right: 15mm;
            height: 20mm;
        }

        .parts-table {
            width: 100%;
            border-collapse: collapse;
        }

        .parts-table td {
            text-align: center;
            color: #000;
            font-size: 8px;
            padding: 2px;
            border: 1px dashed #007bff;
            background: rgba(255, 255, 255, 0.8);
            cursor: text;
        }

        .parts-table td:hover {
            background: rgba(255, 255, 255, 0.95);
        }

        .parts-table td:focus {
            outline: none;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* Print styles */
        @media print {
            .control-panel {
                display: none !important;
            }

            .editable-field {
                border: none !important;
                background: transparent !important;
                box-shadow: none !important;
            }

            .parts-table td {
                border: none !important;
                background: transparent !important;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .tar-container {
                width: 100%;
                height: auto;
                min-height: 297mm;
            }

            .control-panel {
                position: relative;
                top: 0;
                right: 0;
                margin-bottom: 20px;
                width: 100%;
            }
        }

        /* Success message */
        .alert-success {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1001;
            min-width: 300px;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <!-- Control Panel -->
        <div class="control-panel">
            <h5 class="mb-3"><i class="fas fa-edit"></i> TAR Editor</h5>
            <p class="small text-muted mb-3">Klik pada area yang ingin diedit untuk mengisi data</p>
            
            <button type="button" class="btn btn-generate w-100 mb-2" onclick="generatePDF()">
                <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
                <i class="fas fa-file-pdf me-2"></i>
                Generate PDF
            </button>
            
            <button type="button" class="btn btn-outline-secondary w-100 mb-2" onclick="clearAllFields()">
                <i class="fas fa-eraser me-2"></i>
                Clear All
            </button>
            
            <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="addPartRow()">
                <i class="fas fa-plus me-2"></i>
                Add Part Row
            </button>

            <button type="button" class="btn btn-outline-warning w-100" onclick="printPreview()">
                <i class="fas fa-print me-2"></i>
                Print Preview
            </button>
            
            <div class="mt-3 small text-muted">
                <i class="fas fa-info-circle"></i>
                <strong>Tips:</strong>
                <ul class="mt-2 ps-3">
                    <li>Klik area biru untuk mengedit</li>
                    <li>Tekan Enter untuk baris baru</li>
                    <li>Data akan tersimpan otomatis</li>
                </ul>
            </div>
        </div>

        <!-- TAR Template Container -->
        <div class="tar-container">
            <!-- Background Template Image -->
            <div class="template-background">
                <img src="{{ asset('assets/images/tartemplate.png') }}" alt="TAR Template">
            </div>

            <!-- Content Overlay -->
            <div class="content-overlay">
                <!-- Header Form Fields (Top Right) -->
                <div class="header-form">
                    <div class="form-field">PT. PUTERA WIBOWO BORNEO</div>
                    <div class="form-field">AC UNIT</div>
                </div>

                <!-- Data Unit Fields -->
                <div class="data-fields">
                    <table style="font-size: 10px; font-weight: bold;">
                        <tr>
                            <td style="width: 82px; height: 18px; padding: 2px;">
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="unit_code"
                                     data-placeholder="Unit Code"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td style="width: 65px; height: 18px; padding: 2px;"></td>
                            <td style="width: 325px; height: 20px;"></td>
                            <td style="width: 200px; height: 20px;">
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="problem_component"
                                     data-placeholder="Problem Component"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="unit_code_2"
                                     data-placeholder="Unit Code"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_1"
                                     data-placeholder="Teknisi 1"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_3"
                                     data-placeholder="Teknisi 3"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="lifetime_component"
                                     data-placeholder="Lifetime Component"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="hm"
                                     data-placeholder="HM"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_2"
                                     data-placeholder="Teknisi 2"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_4"
                                     data-placeholder="Teknisi 4"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="date_in"
                                     data-placeholder="DD/MM/YYYY"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Problem Description -->
                <div style="left: 20mm; top: 63mm; position: absolute; width: 170mm; height: 10mm;">
                    <div class="editable-field" 
                         contenteditable="true" 
                         data-field="problem_description"
                         data-placeholder="Deskripsi masalah..."
                         style="width: 100%; height: 100%; font-size: 10px;"></div>
                </div>

                <!-- Component Failure -->
                <div style="left: 15mm; top: 77mm; position: absolute; width: 175mm; height: 25mm;">
                    <div class="editable-field" 
                         contenteditable="true" 
                         data-field="component_failure"
                         data-placeholder="Analisis kegagalan komponen..."
                         style="width: 100%; height: 100%; font-size: 9px;"></div>
                </div>

                <!-- Parts Problem Table -->
                <div class="parts-table-container">
                    <table class="parts-table" id="partsTable">
                        <tbody>
                            <tr>
                                <td style="width: 22%;" contenteditable="true" data-field="part_name_1" data-placeholder="Nama Part"></td>
                                <td style="width: 32%;" contenteditable="true" data-field="code_part_1" data-placeholder="Kode Part"></td>
                                <td style="width: 12%;" contenteditable="true" data-field="quantity_1" data-placeholder="Qty"></td>
                                <td style="width: 34%;" contenteditable="true" data-field="remarks_1" data-placeholder="Keterangan"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Plan Fix Section -->
                <div style="top: 233mm; left: 10mm; position: absolute; width: 190mm; height: 15mm;">
                    <div class="editable-field" 
                         contenteditable="true" 
                         data-field="plan_fix"
                         data-placeholder="Rencana perbaikan..."
                         style="width: 100%; height: 100%; font-size: 12px;"></div>
                </div>

                <!-- Plan Recommendation Section -->
                <div style="top: 252mm; left: 10mm; position: absolute; width: 190mm; height: 15mm;">
                    <div class="editable-field" 
                         contenteditable="true" 
                         data-field="plan_rekomen"
                         data-placeholder="Rekomendasi..."
                         style="width: 100%; height: 100%; font-size: 12px;"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set CSRF token for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        
        let partRowCount = 1;

        // Add new part row
        function addPartRow() {
            partRowCount++;
            const tbody = document.querySelector('#partsTable tbody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td style="width: 22%;" contenteditable="true" data-field="part_name_${partRowCount}" data-placeholder="Nama Part"></td>
                <td style="width: 32%;" contenteditable="true" data-field="code_part_${partRowCount}" data-placeholder="Kode Part"></td>
                <td style="width: 12%;" contenteditable="true" data-field="quantity_${partRowCount}" data-placeholder="Qty"></td>
                <td style="width: 34%;" contenteditable="true" data-field="remarks_${partRowCount}" data-placeholder="Keterangan"></td>
            `;
            tbody.appendChild(newRow);
        }

        // Clear all editable fields
        function clearAllFields() {
            if (confirm('Apakah Anda yakin ingin menghapus semua data?')) {
                document.querySelectorAll('[contenteditable="true"]').forEach(field => {
                    field.textContent = '';
                });
                
                // Reset parts table to single row
                const tbody = document.querySelector('#partsTable tbody');
                tbody.innerHTML = `
                    <tr>
                        <td style="width: 22%;" contenteditable="true" data-field="part_name_1" data-placeholder="Nama Part"></td>
                        <td style="width: 32%;" contenteditable="true" data-field="code_part_1" data-placeholder="Kode Part"></td>
                        <td style="width: 12%;" contenteditable="true" data-field="quantity_1" data-placeholder="Qty"></td>
                        <td style="width: 34%;" contenteditable="true" data-field="remarks_1" data-placeholder="Keterangan"></td>
                    </tr>
                `;
                partRowCount = 1;
            }
        }

        // Collect form data
        function collectFormData() {
            const data = {};
            
            // Collect all editable fields
            document.querySelectorAll('[contenteditable="true"]').forEach(field => {
                const fieldName = field.getAttribute('data-field');
                if (fieldName) {
                    data[fieldName] = field.textContent.trim();
                }
            });
            
            // Collect parts data
            const partProblems = [];
            const partRows = document.querySelectorAll('#partsTable tbody tr');
            
            partRows.forEach((row, index) => {
                const cells = row.querySelectorAll('td[contenteditable="true"]');
                if (cells.length === 4) {
                    const partData = {
                        part_name: cells[0].textContent.trim(),
                        code_part: cells[1].textContent.trim(),
                        quantity: cells[2].textContent.trim(),
                        remarks: cells[3].textContent.trim()
                    };
                    
                    // Only add if at least one field has content
                    if (partData.part_name || partData.code_part || partData.quantity || partData.remarks) {
                        partProblems.push(partData);
                    }
                }
            });
            
            data.part_problems = partProblems;
            
            return data;
        }

        // Print preview function
        function printPreview() {
            window.print();
        }

        // Show success message
        function showSuccessMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // Generate PDF
        async function generatePDF() {
            const button = document.querySelector('.btn-generate');
            const spinner = button.querySelector('.loading-spinner');

            // Validate required fields
            const unitCode = document.querySelector('[data-field="unit_code"]').textContent.trim();
            if (!unitCode) {
                alert('Unit Code harus diisi!');
                document.querySelector('[data-field="unit_code"]').focus();
                return;
            }

            // Show loading state
            button.disabled = true;
            spinner.style.display = 'inline-block';

            try {
                const formData = collectFormData();

                const response = await fetch('{{ route("public-tar.generate-pdf") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    // Create blob and download
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `TAR_Public_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showSuccessMessage('PDF berhasil diunduh!');
                } else {
                    const errorData = await response.json();
                    alert('Error: ' + (errorData.message || 'Failed to generate PDF'));
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error generating PDF: ' + error.message);
            } finally {
                // Hide loading state
                button.disabled = false;
                spinner.style.display = 'none';
            }
        }

        // Auto-save functionality and field synchronization
        document.addEventListener('input', function(e) {
            if (e.target.hasAttribute('contenteditable')) {
                // Sync unit code fields
                if (e.target.getAttribute('data-field') === 'unit_code') {
                    const unitCode2Field = document.querySelector('[data-field="unit_code_2"]');
                    if (unitCode2Field) {
                        unitCode2Field.textContent = e.target.textContent;
                    }
                }

                // Auto-save to localStorage
                localStorage.setItem('tar_form_data', JSON.stringify(collectFormData()));
            }
        });

        // Load saved data on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedData = localStorage.getItem('tar_form_data');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    
                    // Restore field values
                    Object.keys(data).forEach(key => {
                        if (key !== 'part_problems') {
                            const field = document.querySelector(`[data-field="${key}"]`);
                            if (field && data[key]) {
                                field.textContent = data[key];
                            }
                        }
                    });
                    
                    // Restore parts data
                    if (data.part_problems && data.part_problems.length > 0) {
                        const tbody = document.querySelector('#partsTable tbody');
                        tbody.innerHTML = '';
                        
                        data.part_problems.forEach((part, index) => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td style="width: 22%;" contenteditable="true" data-field="part_name_${index + 1}">${part.part_name || ''}</td>
                                <td style="width: 32%;" contenteditable="true" data-field="code_part_${index + 1}">${part.code_part || ''}</td>
                                <td style="width: 12%;" contenteditable="true" data-field="quantity_${index + 1}">${part.quantity || ''}</td>
                                <td style="width: 34%;" contenteditable="true" data-field="remarks_${index + 1}">${part.remarks || ''}</td>
                            `;
                            tbody.appendChild(row);
                        });
                        
                        partRowCount = data.part_problems.length;
                    }
                } catch (error) {
                    console.error('Error loading saved data:', error);
                }
            }
        });
    </script>
</body>

</html>
