<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Public Template</title>
    <style>
        @page {
            margin: 0;
            padding: 0;
            size: A4 portrait;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1.2;
            font-weight: bold;
            color: #000;
            position: relative;
        }

        .template-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 210mm;
            height: 297mm;
            z-index: 1;
        }

        .template-background img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .content-overlay {
            position: relative;
            z-index: 2;
            width: 210mm;
            height: 297mm;
            padding: 0;
            margin: 0;
        }

        /* Header Right Form Fields */
        .header-form {
            position: absolute;
            top: 18.5mm;
            right: 15mm;
        }

        .form-field {
            font-size: 12px;
            margin-bottom: 2mm;
            color: #000;
        }

        /* Data Unit Fields */
        .data-fields {
            position: absolute;
            top: 36mm;
            left: 15mm;
            right: 15mm;
        }

        .field-left {
            position: absolute;
            left: 0;
            width: 85mm;
        }

        .field-right {
            position: absolute;
            right: 0;
            width: 100mm;
        }

        .field-item {
            font-size: 10px;
            font-weight: bold;
            color: #000;
        }

        .field-value {
            display: inline-block;
            margin-left: 2mm;
            font-weight: normal;
        }

        /* Problem Issue Section */
        .problem-section {
            position: absolute;
            top: 145mm;
            left: 15mm;
            right: 15mm;
            height: 25mm;
        }

        .problem-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }

        /* Parts Failure Analysis Section */
        .analysis-section {
            position: absolute;
            top: 175mm;
            left: 15mm;
            right: 15mm;
            height: 30mm;
        }

        .analysis-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }

        .parts-table {
            width: 100%;
            border-collapse: collapse;
        }

        .parts-table td {
            text-align: center;
            justify-content: center;
            align-items: center;
            color: #000;
            font-size: 8px;
        }

        /* Picture Component Failure Section */
        .picture-section {
            top: 138mm;
            left: 15mm;
        }

        .picture-label {
            font-size: 6px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1mm;
            color: #000;
        }

        .picture-content img {
            border: 1px solid #ccc;
        }

        /* Correction Action Section */
        .correction-section {
            position: absolute;
            top: 265mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .correction-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }

        /* Recommendation Section */
        .recommendation-section {
            position: absolute;
            top: 280mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .recommendation-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }

        table td {
            border: rgba(155, 252, 0, 0) solid 2.4px;
        }
    </style>
</head>

<body>
    <!-- Background Template Image -->
    <div class="template-background">
        <img src="{{ public_path('assets/images/tartemplate.png') }}" alt="TAR Template">
    </div>

    <!-- Content Overlay -->
    <div class="content-overlay">

        <!-- Header Form Fields (Top Right) -->
        <div class="header-form">
            <div class="form-field">PT. PUTERA WIBOWO BORNEO</div>
            <div class="form-field">AC UNIT</div>
        </div>

        <!-- Data Unit Fields -->
        <div class="data-fields">
            <table style="font-size: 10px; font-weight: bold; border:rgba(155, 252, 0, 0) solid 2px; width: 100%;">
                <tr>
                    <td style="width: 15%;height: 18px; padding: 2px; align-items: center;">{{ $tarData['unit_code'] ?? '' }}</td>
                    <td style="width: 12%;height: 18px; padding: 2px;"></td>
                    <td style="width: 48%;height: 20px;"></td>
                    <td style="width: 25%;height: 20px;">{{ $tarData['problem_component'] ?? '' }}</td>
                </tr>
                <tr>
                    <td>{{ $tarData['unit_code_2'] ?? $tarData['unit_code'] ?? '' }}</td>
                    <td>{{ $tarData['technician_1'] ?? '' }}</td>
                    <td>{{ $tarData['technician_3'] ?? '' }}</td>
                    <td>{{ $tarData['lifetime_component'] ?? '' }}</td>
                </tr>
                <tr>
                    <td>{{ $tarData['hm'] ?? '' }}</td>
                    <td>{{ $tarData['technician_2'] ?? '' }}</td>
                    <td>{{ $tarData['technician_4'] ?? '' }}</td>
                    <td>{{ $tarData['date_in'] ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Problem Description -->
        <div style="left: 15mm; top: 63mm; position: absolute; right: 15mm;">
            <table style="width: 100%;">
                <tr>
                    <td>{{ $tarData['problem_description'] ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Component Failure Analysis -->
        <div style="left: 15mm; top: 77mm; position: absolute; right: 15mm;">
            <table style="width: 100%;">
                <tr>
                    <td>{{ $tarData['component_failure'] ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Main Parts Problem Table -->
        <div style="position: absolute;
            top: 110.5mm;
            left: 15mm;
            right: 15mm;
            height: 20mm;">
            <table class="parts-table">
                <tbody>
                    @if(isset($tarData['part_problems']) && is_array($tarData['part_problems']))
                        @foreach($tarData['part_problems'] as $partProblem)
                            @if(!empty($partProblem['part_name']) || !empty($partProblem['code_part']) || !empty($partProblem['quantity']) || !empty($partProblem['remarks']))
                            <tr>
                                <td style="width: 22%;height: 13px;">{{ $partProblem['part_name'] ?? '' }}</td>
                                <td style="width: 32%;">{{ $partProblem['code_part'] ?? '' }}</td>
                                <td style="width: 12%;">{{ $partProblem['quantity'] ?? '' }}</td>
                                <td style="width: 34%;">{{ $partProblem['remarks'] ?? '' }}</td>
                            </tr>
                            @endif
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Picture Component Failure Section -->
        <div class="picture-section">
            <div style="top: 140mm; left: 15mm; position: absolute; width: 180mm; height: 80mm;">
                <table style="width: 100%; height: 100%;">
                    <tr>
                        <td style="width: 50%; padding: 5px; text-align: center; vertical-align: top;">
                            @if(isset($tarData['image_1']) && $tarData['image_1'])
                                <div style="text-align: center; font-size: 12px; margin-bottom: 5px;">Gambar Sebelum</div>
                                <img style="width: 80mm; height: 60mm; object-fit: cover; border: 1px solid #ccc;" src="{{ $tarData['image_1'] }}" alt="Gambar Sebelum">
                            @endif
                        </td>
                        <td style="width: 50%; padding: 5px; text-align: center; vertical-align: top;">
                            @if(isset($tarData['image_2']) && $tarData['image_2'])
                                <div style="text-align: center; font-size: 12px; margin-bottom: 5px;">Gambar Sesudah</div>
                                <img style="width: 80mm; height: 60mm; object-fit: cover; border: 1px solid #ccc;" src="{{ $tarData['image_2'] }}" alt="Gambar Sesudah">
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 50%; padding: 5px; text-align: center; vertical-align: top;">
                            @if(isset($tarData['image_3']) && $tarData['image_3'])
                                <div style="text-align: center; font-size: 12px; margin-bottom: 5px;">Gambar Unit</div>
                                <img style="width: 80mm; height: 60mm; object-fit: cover; border: 1px solid #ccc;" src="{{ $tarData['image_3'] }}" alt="Gambar Unit">
                            @endif
                        </td>
                        <td style="width: 50%; padding: 5px; text-align: center; vertical-align: top;">
                            @if(isset($tarData['image_4']) && $tarData['image_4'])
                                <div style="text-align: center; font-size: 12px; margin-bottom: 5px;">Gambar Tambahan</div>
                                <img style="width: 80mm; height: 60mm; object-fit: cover; border: 1px solid #ccc;" src="{{ $tarData['image_4'] }}" alt="Gambar Tambahan">
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Plan Fix Section -->
        <div style="top: 233mm; left: 15mm; right: 15mm; position: absolute;">
            <table style="width: 100%; font-size: 12px;">
                <tr>
                    <td style="height: 10px;">{{ $tarData['plan_fix'] ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Plan Recommendation Section -->
        <div style="top: 252mm; left: 15mm; right: 15mm; position: absolute;">
            <table style="width: 100%; font-size: 12px;">
                <tr>
                    <td style="height: 10px;">{{ $tarData['plan_rekomen'] ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Recommendation Preventive Action Section -->
        <div class="recommendation-section">
            <div class="recommendation-content">
                <!-- This section can be filled manually or from additional data -->
            </div>
        </div>

    </div>
</body>

</html>
