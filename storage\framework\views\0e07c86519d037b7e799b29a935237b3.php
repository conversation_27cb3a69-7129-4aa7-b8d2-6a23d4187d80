<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Template - Editable Form</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @page {
            margin: 0;
            padding: 0;
            size: A4 portrait;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            overflow-x: auto;
        }

        .main-container {
            display: flex;
            min-height: 100vh;
            gap: 20px;
            padding: 20px;
        }

        .tar-section {
            flex: 1;
            overflow-x: auto;
            overflow-y: auto;
            padding: 20px;
            min-width: 0;
        }

        .tar-container {
            position: relative;
            width: 210mm;
            height: 297mm;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: visible;
            flex-shrink: 0;
            margin: 0 auto;
        }

        .menu-section {
            width: 300px;
            flex-shrink: 0;
        }

        .template-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .template-background img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .content-overlay {
            position: relative;
            z-index: 2;
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
        }

        .editable-field {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border: 1px dashed #007bff;
            padding: 2px 4px;
            font-size: 10px;
            font-weight: bold;
            color: #000;
            min-height: 16px;
            cursor: text;
            transition: all 0.3s ease;
        }

        .editable-field:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: #0056b3;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
        }

        .editable-field:focus {
            outline: none;
            background: rgba(255, 255, 255, 1);
            border-color: #0056b3;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .editable-field {
                min-height: 20px;
                padding: 4px 6px;
                font-size: 12px;
            }

            .btn-add-part {
                padding: 8px 12px;
                font-size: 14px;
            }
        }

        .editable-field[contenteditable="true"]:empty:before {
            content: attr(data-placeholder);
            color: #6c757d;
            font-style: italic;
        }

        /* Header Form Fields */
        .header-form {
            position: absolute;
            top: 18.5mm;
            right: 15mm;
        }

        .form-field {
            font-size: 12px;
            margin-bottom: 2mm;
            color: #000;
        }

        /* Data Unit Fields */
        .data-fields {
            position: absolute;
            top: 36mm;
            left: 15mm;
            right: 15mm;
        }

        /* Control Panel */
        .control-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            position: sticky;
            top: 20px;
            height: fit-content;
        }

        .btn-generate {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        }

        .loading-spinner {
            display: none;
        }

        /* Part Problems Table */
        .parts-table-container {
            position: absolute;
            top: 110.5mm;
            left: 15mm;
            right: 15mm;
            height: 20mm;
        }

        .parts-table {
            width: 100%;
            border-collapse: collapse;
        }

        .parts-table td {
            text-align: center;
            color: #000;
            font-size: 8px;
            padding: 2px;
            border: 1px dashed #007bff;
            background: rgba(255, 255, 255, 0.8);
            cursor: text;
        }

        .parts-table td:hover {
            background: rgba(255, 255, 255, 0.95);
        }

        .parts-table td:focus {
            outline: none;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* Add Part Button */
        .add-part-btn {
            position: absolute;
            top: 108mm;
            right: 15mm;
            z-index: 10;
        }

        .btn-add-part {
            background: #28a745;
            border: none;
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-add-part:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .btn-add-part:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Pulse animation for add button */
        @keyframes pulse {
            0% { box-shadow: 0 2px 6px rgba(0,0,0,0.2), 0 0 0 0 rgba(40, 167, 69, 0.7); }
            70% { box-shadow: 0 2px 6px rgba(0,0,0,0.2), 0 0 0 10px rgba(40, 167, 69, 0); }
            100% { box-shadow: 0 2px 6px rgba(0,0,0,0.2), 0 0 0 0 rgba(40, 167, 69, 0); }
        }

        .btn-add-part.pulse {
            animation: pulse 2s infinite;
        }

        /* Autocomplete Dropdown */
        .autocomplete-container {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .autocomplete-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 0 0 4px 4px;
        }

        .autocomplete-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            font-size: 11px;
            line-height: 1.3;
        }

        .autocomplete-item:hover {
            background-color: #f8f9fa;
        }

        .autocomplete-item.selected {
            background-color: #007bff;
            color: white;
        }

        .autocomplete-item:last-child {
            border-bottom: none;
        }

        .autocomplete-loading {
            padding: 8px 12px;
            text-align: center;
            color: #6c757d;
            font-style: italic;
            font-size: 11px;
        }

        .autocomplete-no-results {
            padding: 8px 12px;
            text-align: center;
            color: #6c757d;
            font-style: italic;
            font-size: 11px;
        }

        /* Image Upload Section */
        .image-upload-section {
            position: absolute;
            top: 140mm;
            left: 15mm;
            right: 15mm;
            height: 80mm;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            height: 100%;
        }

        .image-upload-box {
            border: 2px dashed #007bff;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            position: relative;
            min-height: 120px;
        }

        .image-upload-box:hover {
            border-color: #0056b3;
            background: rgba(0, 123, 255, 0.1);
        }

        .image-upload-box.has-image {
            border-color: #28a745;
            border-style: solid;
        }

        .image-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 6px;
        }

        .image-upload-text {
            text-align: center;
            color: #6c757d;
            font-size: 11px;
            margin-top: 8px;
        }

        .image-upload-icon {
            font-size: 24px;
            color: #007bff;
            margin-bottom: 8px;
        }

        .image-remove-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            cursor: pointer;
            display: none;
        }

        .image-upload-box.has-image .image-remove-btn {
            display: block;
        }

        .image-upload-box.has-image .image-upload-content {
            display: none;
        }

        .hidden-file-input {
            display: none;
        }

        /* Print styles */
        @media print {
            .menu-section {
                display: none !important;
            }

            .main-container {
                padding: 0;
                gap: 0;
            }

            .tar-section {
                min-width: auto;
                overflow: visible;
            }

            .editable-field {
                border: none !important;
                background: transparent !important;
                box-shadow: none !important;
            }

            .parts-table td {
                border: none !important;
                background: transparent !important;
            }

            .add-part-btn {
                display: none !important;
            }
        }

        /* Mobile scroll indicator */
        .mobile-scroll-hint {
            display: none;
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: rgba(0, 123, 255, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 12px;
            z-index: 1001;
            animation: fadeInOut 4s ease-in-out;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        /* Custom scrollbar for all devices */
        .tar-section::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .tar-section::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .tar-section::-webkit-scrollbar-thumb {
            background: #007bff;
            border-radius: 4px;
        }

        .tar-section::-webkit-scrollbar-thumb:hover {
            background: #0056b3;
        }

        .tar-section::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                padding: 10px;
                gap: 10px;
            }

            .tar-section {
                padding: 10px;
                min-height: 400px;
            }

            .tar-container {
                margin: 0;
            }

            .menu-section {
                width: 100%;
                order: -1;
            }

            .control-panel {
                position: relative;
                top: 0;
                margin-bottom: 0;
            }

            .mobile-scroll-hint {
                display: block;
            }
        }

        /* Tablet adjustments */
        @media (max-width: 1024px) and (min-width: 769px) {
            .main-container {
                padding: 15px;
            }

            .menu-section {
                width: 250px;
            }
        }

        /* Success message */
        .alert-success {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1001;
            min-width: 300px;
            text-align: center;
        }
    </style>
</head>

<body>
    <div class="main-container">
        <!-- Menu Section -->
        <div class="menu-section">
            <div class="control-panel">
                <h5 class="mb-3"><i class="fas fa-edit"></i> TAR Editor</h5>
                <p class="small text-muted mb-3">Klik pada area yang ingin diedit untuk mengisi data</p>

                <button type="button" class="btn btn-generate w-100 mb-2" onclick="generatePDF()">
                    <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
                    <i class="fas fa-file-pdf me-2"></i>
                    Generate PDF
                </button>

                <button type="button" class="btn btn-outline-secondary w-100 mb-2" onclick="clearAllFields()">
                    <i class="fas fa-eraser me-2"></i>
                    Clear All
                </button>

                <div class="mt-3 small text-muted">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tips:</strong>
                    <ul class="mt-2 ps-3">
                        <li>Klik area biru untuk mengedit</li>
                        <li>Tekan Enter untuk baris baru</li>
                        <li>Data akan tersimpan otomatis</li>
                        <li>Tombol + hijau untuk tambah part</li>
                        <li>Mobile: geser kiri-kanan untuk melihat TAR</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- TAR Section -->
        <div class="tar-section">
            <div class="tar-container">
            <!-- Background Template Image -->
            <div class="template-background">
                <img src="<?php echo e(asset('assets/images/tartemplate.png')); ?>" alt="TAR Template">
            </div>

            <!-- Content Overlay -->
            <div class="content-overlay">
                <!-- Header Form Fields (Top Right) -->
                <div class="header-form">
                    <div class="form-field">PT. PUTERA WIBOWO BORNEO</div>
                    <div class="form-field">AC UNIT</div>
                </div>

                <!-- Data Unit Fields -->
                <div class="data-fields">
                    <table style="font-size: 10px; font-weight: bold; width: 100%;">
                        <tr>
                            <td style="width: 15%; height: 18px; padding: 2px;">
                                <div class="editable-field"
                                     contenteditable="true"
                                     data-field="unit_code"
                                     data-placeholder="Unit Code"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td style="width: 12%; height: 18px; padding: 2px;"></td>
                            <td style="width: 48%; height: 20px;"></td>
                            <td style="width: 25%; height: 20px;">
                                <div class="editable-field"
                                     contenteditable="true"
                                     data-field="problem_component"
                                     data-placeholder="Problem Component"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="unit_code_2"
                                     data-placeholder="Unit Code"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_1"
                                     data-placeholder="Teknisi 1"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_3"
                                     data-placeholder="Teknisi 3"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="lifetime_component"
                                     data-placeholder="Lifetime Component"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="hm"
                                     data-placeholder="HM"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_2"
                                     data-placeholder="Teknisi 2"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="technician_4"
                                     data-placeholder="Teknisi 4"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                            <td>
                                <div class="editable-field" 
                                     contenteditable="true" 
                                     data-field="date_in"
                                     data-placeholder="DD/MM/YYYY"
                                     style="position: relative; width: 100%; height: 100%; border: none; background: transparent;"></div>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Problem Description -->
                <div style="left: 15mm; top: 63mm; position: absolute; right: 15mm; height: 10mm;">
                    <div class="editable-field"
                         contenteditable="true"
                         data-field="problem_description"
                         data-placeholder="Deskripsi masalah..."
                         style="width: 100%; height: 100%; font-size: 10px;"></div>
                </div>

                <!-- Component Failure -->
                <div style="left: 15mm; top: 77mm; position: absolute; right: 15mm; height: 25mm;">
                    <div class="editable-field"
                         contenteditable="true"
                         data-field="component_failure"
                         data-placeholder="Analisis kegagalan komponen..."
                         style="width: 100%; height: 100%; font-size: 9px;"></div>
                </div>

                <!-- Add Part Button -->
                <div class="add-part-btn">
                    <button type="button" class="btn-add-part pulse" onclick="addPartRow()" title="Tambah Baris Part">
                        <i class="fas fa-plus"></i>
                        <span class="d-none d-sm-inline">Part</span>
                    </button>
                </div>

                <!-- Parts Problem Table -->
                <div class="parts-table-container">
                    <table class="parts-table" id="partsTable">
                        <tbody>
                            <tr>
                                <td style="width: 22%; position: relative;">
                                    <div class="autocomplete-container">
                                        <div contenteditable="true"
                                             data-field="part_name_1"
                                             data-placeholder="Nama Part"
                                             class="part-name-field"
                                             data-row="1"></div>
                                        <div class="autocomplete-dropdown" style="display: none;"></div>
                                    </div>
                                </td>
                                <td style="width: 32%;" contenteditable="true" data-field="code_part_1" data-placeholder="Kode Part" class="code-part-field" data-row="1"></td>
                                <td style="width: 12%;" contenteditable="true" data-field="quantity_1" data-placeholder="Qty" class="quantity-field" data-row="1"></td>
                                <td style="width: 34%;" contenteditable="true" data-field="remarks_1" data-placeholder="Keterangan"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Image Upload Section -->
                <div class="image-upload-section">
                    <div class="image-grid">
                        <div class="image-upload-box" onclick="triggerImageUpload(1)">
                            <div class="image-upload-content">
                                <i class="fas fa-camera image-upload-icon"></i>
                                <div class="image-upload-text">
                                    <strong>Gambar Sebelum</strong><br>
                                    Klik untuk foto/upload
                                </div>
                            </div>
                            <img class="image-preview" style="display: none;" />
                            <button class="image-remove-btn" onclick="removeImage(1, event)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="image-upload-box" onclick="triggerImageUpload(2)">
                            <div class="image-upload-content">
                                <i class="fas fa-camera image-upload-icon"></i>
                                <div class="image-upload-text">
                                    <strong>Gambar Sesudah</strong><br>
                                    Klik untuk foto/upload
                                </div>
                            </div>
                            <img class="image-preview" style="display: none;" />
                            <button class="image-remove-btn" onclick="removeImage(2, event)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="image-upload-box" onclick="triggerImageUpload(3)">
                            <div class="image-upload-content">
                                <i class="fas fa-camera image-upload-icon"></i>
                                <div class="image-upload-text">
                                    <strong>Gambar Unit</strong><br>
                                    Klik untuk foto/upload
                                </div>
                            </div>
                            <img class="image-preview" style="display: none;" />
                            <button class="image-remove-btn" onclick="removeImage(3, event)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="image-upload-box" onclick="triggerImageUpload(4)">
                            <div class="image-upload-content">
                                <i class="fas fa-camera image-upload-icon"></i>
                                <div class="image-upload-text">
                                    <strong>Gambar Tambahan</strong><br>
                                    Klik untuk foto/upload
                                </div>
                            </div>
                            <img class="image-preview" style="display: none;" />
                            <button class="image-remove-btn" onclick="removeImage(4, event)">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Plan Fix Section -->
                <div style="top: 233mm; left: 15mm; right: 15mm; position: absolute; height: 15mm;">
                    <div class="editable-field"
                         contenteditable="true"
                         data-field="plan_fix"
                         data-placeholder="Rencana perbaikan..."
                         style="width: 100%; height: 100%; font-size: 12px;"></div>
                </div>

                <!-- Plan Recommendation Section -->
                <div style="top: 252mm; left: 15mm; right: 15mm; position: absolute; height: 15mm;">
                    <div class="editable-field"
                         contenteditable="true"
                         data-field="plan_rekomen"
                         data-placeholder="Rekomendasi..."
                         style="width: 100%; height: 100%; font-size: 12px;"></div>
                </div>
            </div>
            </div>
        </div>
    </div>

    <!-- Mobile Scroll Hint -->
    <div class="mobile-scroll-hint" id="mobileScrollHint">
        <i class="fas fa-arrows-alt-h me-1"></i>
        Geser untuk melihat seluruh form
    </div>

    <!-- Hidden File Inputs -->
    <input type="file" id="imageInput1" class="hidden-file-input" accept="image/*" capture="environment" onchange="handleImageUpload(1, this)">
    <input type="file" id="imageInput2" class="hidden-file-input" accept="image/*" capture="environment" onchange="handleImageUpload(2, this)">
    <input type="file" id="imageInput3" class="hidden-file-input" accept="image/*" capture="environment" onchange="handleImageUpload(3, this)">
    <input type="file" id="imageInput4" class="hidden-file-input" accept="image/*" capture="environment" onchange="handleImageUpload(4, this)">

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set CSRF token for AJAX requests
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        let partRowCount = 1;
        let uploadedImages = {}; // Store uploaded images

        // Image upload functionality
        function triggerImageUpload(imageNumber) {
            const input = document.getElementById(`imageInput${imageNumber}`);
            input.click();
        }

        function handleImageUpload(imageNumber, input) {
            const file = input.files[0];
            if (!file) return;

            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Harap pilih file gambar yang valid');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Ukuran file terlalu besar. Maksimal 5MB');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const imageBox = document.querySelector(`.image-upload-box:nth-child(${imageNumber})`);
                const preview = imageBox.querySelector('.image-preview');

                preview.src = e.target.result;
                preview.style.display = 'block';
                imageBox.classList.add('has-image');

                // Store image data
                uploadedImages[imageNumber] = {
                    file: file,
                    dataUrl: e.target.result,
                    name: file.name
                };

                // Save to localStorage
                saveImagesToStorage();
            };

            reader.readAsDataURL(file);
        }

        function removeImage(imageNumber, event) {
            event.stopPropagation();

            const imageBox = document.querySelector(`.image-upload-box:nth-child(${imageNumber})`);
            const preview = imageBox.querySelector('.image-preview');
            const input = document.getElementById(`imageInput${imageNumber}`);

            preview.src = '';
            preview.style.display = 'none';
            imageBox.classList.remove('has-image');
            input.value = '';

            // Remove from storage
            delete uploadedImages[imageNumber];
            saveImagesToStorage();
        }

        function saveImagesToStorage() {
            const imageData = {};
            Object.keys(uploadedImages).forEach(key => {
                imageData[key] = {
                    dataUrl: uploadedImages[key].dataUrl,
                    name: uploadedImages[key].name
                };
            });
            localStorage.setItem('tar_images', JSON.stringify(imageData));
        }

        function loadImagesFromStorage() {
            const savedImages = localStorage.getItem('tar_images');
            if (savedImages) {
                try {
                    const imageData = JSON.parse(savedImages);
                    Object.keys(imageData).forEach(imageNumber => {
                        const imageBox = document.querySelector(`.image-upload-box:nth-child(${imageNumber})`);
                        const preview = imageBox.querySelector('.image-preview');

                        preview.src = imageData[imageNumber].dataUrl;
                        preview.style.display = 'block';
                        imageBox.classList.add('has-image');

                        uploadedImages[imageNumber] = imageData[imageNumber];
                    });
                } catch (error) {
                    console.error('Error loading images:', error);
                }
            }
        }

        // Autocomplete functionality
        let searchTimeout;
        let currentDropdown = null;
        let selectedIndex = -1;

        function initializeAutocomplete() {
            // Add event listeners to existing part name fields
            document.querySelectorAll('.part-name-field').forEach(field => {
                setupPartNameField(field);
            });
        }

        function setupPartNameField(field) {
            field.addEventListener('input', function(e) {
                handlePartNameInput(e.target);
            });

            field.addEventListener('keydown', function(e) {
                handlePartNameKeydown(e);
            });

            field.addEventListener('blur', function(e) {
                // Delay hiding dropdown to allow for clicks
                setTimeout(() => {
                    hideDropdown();
                }, 200);
            });
        }

        function handlePartNameInput(field) {
            const query = field.textContent.trim();
            const dropdown = field.parentElement.querySelector('.autocomplete-dropdown');

            if (query.length < 2) {
                hideDropdown();
                return;
            }

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchParts(query, dropdown, field);
            }, 300); // Debounce delay
        }

        function handlePartNameKeydown(e) {
            const dropdown = currentDropdown;
            if (!dropdown || dropdown.style.display === 'none') return;

            const items = dropdown.querySelectorAll('.autocomplete-item');

            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                    updateSelection(items);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    updateSelection(items);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && items[selectedIndex]) {
                        selectPart(items[selectedIndex]);
                    }
                    break;
                case 'Escape':
                    hideDropdown();
                    break;
            }
        }

        function updateSelection(items) {
            items.forEach((item, index) => {
                item.classList.toggle('selected', index === selectedIndex);
            });
        }

        async function searchParts(query, dropdown, field) {
            try {
                dropdown.innerHTML = '<div class="autocomplete-loading">Mencari...</div>';
                dropdown.style.display = 'block';
                currentDropdown = dropdown;

                const response = await fetch(`<?php echo e(route('public-tar.search-parts')); ?>?q=${encodeURIComponent(query)}`);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                // Handle error response
                if (data.success === false) {
                    dropdown.innerHTML = '<div class="autocomplete-no-results">Error: ' + data.message + '</div>';
                    return;
                }

                // Handle empty results
                const parts = data.results || data;
                if (!Array.isArray(parts) || parts.length === 0) {
                    dropdown.innerHTML = '<div class="autocomplete-no-results">Tidak ada part ditemukan</div>';
                    return;
                }

                dropdown.innerHTML = '';
                selectedIndex = -1;

                parts.forEach((part, index) => {
                    const item = document.createElement('div');
                    item.className = 'autocomplete-item';
                    item.innerHTML = `
                        <div style="font-weight: bold;">${part.short_name || part.part_name || 'Unknown'}</div>
                        <div style="font-size: 10px; color: #666;">${part.code_part || 'No Code'}</div>
                    `;

                    item.addEventListener('click', () => {
                        selectPart(item, part, field);
                    });

                    // Store part data
                    item.dataset.partData = JSON.stringify(part);
                    dropdown.appendChild(item);
                });

            } catch (error) {
                console.error('Search error:', error);
                dropdown.innerHTML = '<div class="autocomplete-no-results">Error dalam pencarian: ' + error.message + '</div>';
            }
        }

        function selectPart(item, partData = null, field = null) {
            if (!partData) {
                partData = JSON.parse(item.dataset.partData);
            }

            if (!field) {
                field = currentDropdown.parentElement.querySelector('.part-name-field');
            }

            // Fill part name with short name
            field.textContent = partData.short_name;

            // Get row number
            const rowNum = field.dataset.row;

            // Fill code part field
            const codeField = document.querySelector(`[data-field="code_part_${rowNum}"]`);
            if (codeField) {
                codeField.textContent = partData.code_part;
            }

            // Fill quantity field with default value "1"
            const qtyField = document.querySelector(`[data-field="quantity_${rowNum}"]`);
            if (qtyField) {
                qtyField.textContent = '1';
            }

            hideDropdown();

            // Save to localStorage
            localStorage.setItem('tar_form_data', JSON.stringify(collectFormData()));
        }

        function hideDropdown() {
            if (currentDropdown) {
                currentDropdown.style.display = 'none';
                currentDropdown = null;
                selectedIndex = -1;
            }
        }

        // Add new part row
        function addPartRow() {
            partRowCount++;
            const tbody = document.querySelector('#partsTable tbody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td style="width: 22%; position: relative;">
                    <div class="autocomplete-container">
                        <div contenteditable="true"
                             data-field="part_name_${partRowCount}"
                             data-placeholder="Nama Part"
                             class="part-name-field"
                             data-row="${partRowCount}"></div>
                        <div class="autocomplete-dropdown" style="display: none;"></div>
                    </div>
                </td>
                <td style="width: 32%;" contenteditable="true" data-field="code_part_${partRowCount}" data-placeholder="Kode Part" class="code-part-field" data-row="${partRowCount}"></td>
                <td style="width: 12%;" contenteditable="true" data-field="quantity_${partRowCount}" data-placeholder="Qty" class="quantity-field" data-row="${partRowCount}"></td>
                <td style="width: 34%;" contenteditable="true" data-field="remarks_${partRowCount}" data-placeholder="Keterangan"></td>
            `;
            tbody.appendChild(newRow);

            // Setup autocomplete for new row
            const newPartField = newRow.querySelector('.part-name-field');
            setupPartNameField(newPartField);

            // Remove pulse animation after first use
            const addButton = document.querySelector('.btn-add-part');
            if (addButton) {
                addButton.classList.remove('pulse');
            }

            // Focus on the first cell of the new row
            if (newPartField) {
                newPartField.focus();
            }
        }

        // Clear all editable fields
        function clearAllFields() {
            if (confirm('Apakah Anda yakin ingin menghapus semua data?')) {
                document.querySelectorAll('[contenteditable="true"]').forEach(field => {
                    field.textContent = '';
                });

                // Reset parts table to single row
                const tbody = document.querySelector('#partsTable tbody');
                tbody.innerHTML = `
                    <tr>
                        <td style="width: 22%; position: relative;">
                            <div class="autocomplete-container">
                                <div contenteditable="true"
                                     data-field="part_name_1"
                                     class="part-name-field"
                                     data-row="1"></div>
                                <div class="autocomplete-dropdown" style="display: none;"></div>
                            </div>
                        </td>
                        <td style="width: 32%;" contenteditable="true" data-field="code_part_1" data-placeholder="Kode Part" class="code-part-field" data-row="1"></td>
                        <td style="width: 12%;" contenteditable="true" data-field="quantity_1" data-placeholder="Qty" class="quantity-field" data-row="1"></td>
                        <td style="width: 34%;" contenteditable="true" data-field="remarks_1" data-placeholder="Keterangan"></td>
                    </tr>
                `;
                partRowCount = 1;

                // Setup autocomplete for the reset row
                const partField = tbody.querySelector('.part-name-field');
                setupPartNameField(partField);

                // Clear all images
                for (let i = 1; i <= 4; i++) {
                    removeImage(i, { stopPropagation: () => {} });
                }

                // Clear localStorage
                localStorage.removeItem('tar_form_data');
                localStorage.removeItem('tar_images');
            }
        }

        // Collect form data
        function collectFormData() {
            const data = {};
            
            // Collect all editable fields
            document.querySelectorAll('[contenteditable="true"]').forEach(field => {
                const fieldName = field.getAttribute('data-field');
                if (fieldName) {
                    data[fieldName] = field.textContent.trim();
                }
            });
            
            // Collect parts data
            const partProblems = [];
            const partRows = document.querySelectorAll('#partsTable tbody tr');
            
            partRows.forEach((row, index) => {
                const cells = row.querySelectorAll('td[contenteditable="true"]');
                if (cells.length === 4) {
                    const partData = {
                        part_name: cells[0].textContent.trim(),
                        code_part: cells[1].textContent.trim(),
                        quantity: cells[2].textContent.trim(),
                        remarks: cells[3].textContent.trim()
                    };
                    
                    // Only add if at least one field has content
                    if (partData.part_name || partData.code_part || partData.quantity || partData.remarks) {
                        partProblems.push(partData);
                    }
                }
            });
            
            data.part_problems = partProblems;

            // Add images data
            Object.keys(uploadedImages).forEach(imageNumber => {
                data[`image_${imageNumber}`] = uploadedImages[imageNumber].dataUrl;
            });

            return data;
        }

        // Show success message
        function showSuccessMessage(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);

            // Auto remove after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // Generate PDF
        async function generatePDF() {
            const button = document.querySelector('.btn-generate');
            const spinner = button.querySelector('.loading-spinner');

            // Validate required fields
            const unitCode = document.querySelector('[data-field="unit_code"]').textContent.trim();
            if (!unitCode) {
                alert('Unit Code harus diisi!');
                document.querySelector('[data-field="unit_code"]').focus();
                return;
            }

            // Show loading state
            button.disabled = true;
            spinner.style.display = 'inline-block';

            try {
                const formData = collectFormData();

                const response = await fetch('<?php echo e(route("public-tar.generate-pdf")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    // Create blob and download
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `TAR_Public_${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    showSuccessMessage('PDF berhasil diunduh!');
                } else {
                    const errorData = await response.json();
                    alert('Error: ' + (errorData.message || 'Failed to generate PDF'));
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Error generating PDF: ' + error.message);
            } finally {
                // Hide loading state
                button.disabled = false;
                spinner.style.display = 'none';
            }
        }

        // Auto-save functionality and field synchronization
        document.addEventListener('input', function(e) {
            if (e.target.hasAttribute('contenteditable')) {
                // Sync unit code fields
                if (e.target.getAttribute('data-field') === 'unit_code') {
                    const unitCode2Field = document.querySelector('[data-field="unit_code_2"]');
                    if (unitCode2Field) {
                        unitCode2Field.textContent = e.target.textContent;
                    }
                }

                // Auto-save to localStorage
                localStorage.setItem('tar_form_data', JSON.stringify(collectFormData()));
            }
        });

        // Hide mobile scroll hint on interaction
        function hideMobileScrollHint() {
            const hint = document.getElementById('mobileScrollHint');
            if (hint) {
                hint.style.display = 'none';
            }
        }

        // Show mobile scroll hint on mobile devices
        function showMobileScrollHint() {
            if (window.innerWidth <= 768) {
                const hint = document.getElementById('mobileScrollHint');
                if (hint) {
                    hint.style.display = 'block';
                    setTimeout(hideMobileScrollHint, 4000);
                }
            }
        }

        // Load saved data on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize autocomplete
            initializeAutocomplete();

            // Load saved images
            loadImagesFromStorage();

            const savedData = localStorage.getItem('tar_form_data');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);

                    // Restore field values
                    Object.keys(data).forEach(key => {
                        if (key !== 'part_problems') {
                            const field = document.querySelector(`[data-field="${key}"]`);
                            if (field && data[key]) {
                                field.textContent = data[key];
                            }
                        }
                    });

                    // Restore parts data
                    if (data.part_problems && data.part_problems.length > 0) {
                        const tbody = document.querySelector('#partsTable tbody');
                        tbody.innerHTML = '';

                        data.part_problems.forEach((part, index) => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td style="width: 22%; position: relative;">
                                    <div class="autocomplete-container">
                                        <div contenteditable="true"
                                             data-field="part_name_${index + 1}"
                                             class="part-name-field"
                                             data-row="${index + 1}">${part.part_name || ''}</div>
                                        <div class="autocomplete-dropdown" style="display: none;"></div>
                                    </div>
                                </td>
                                <td style="width: 32%;" contenteditable="true" data-field="code_part_${index + 1}" class="code-part-field" data-row="${index + 1}">${part.code_part || ''}</td>
                                <td style="width: 12%;" contenteditable="true" data-field="quantity_${index + 1}" class="quantity-field" data-row="${index + 1}">${part.quantity || ''}</td>
                                <td style="width: 34%;" contenteditable="true" data-field="remarks_${index + 1}">${part.remarks || ''}</td>
                            `;
                            tbody.appendChild(row);

                            // Setup autocomplete for restored row
                            const partField = row.querySelector('.part-name-field');
                            setupPartNameField(partField);
                        });

                        partRowCount = data.part_problems.length;
                    }
                } catch (error) {
                    console.error('Error loading saved data:', error);
                }
            }

            // Add event listeners for mobile scroll hint
            const tarSection = document.querySelector('.tar-section');
            if (tarSection) {
                tarSection.addEventListener('scroll', hideMobileScrollHint);
                tarSection.addEventListener('touchstart', hideMobileScrollHint);
                tarSection.addEventListener('click', hideMobileScrollHint);
            }

            // Show mobile scroll hint on mobile devices
            showMobileScrollHint();

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.autocomplete-container')) {
                    hideDropdown();
                }
            });
        });
    </script>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/public-tar/editable-template.blade.php ENDPATH**/ ?>