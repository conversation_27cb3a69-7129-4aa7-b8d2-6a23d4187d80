<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class PublicTarController extends Controller
{
    /**
     * Display the public editable TAR template form.
     */
    public function index()
    {
        return view('public-tar.editable-template');
    }

    /**
     * Generate PDF from the submitted TAR form data.
     */
    public function generatePdf(Request $request)
    {
        // Validate the incoming request data
        $validator = Validator::make($request->all(), [
            'unit_code' => 'required|string|max:255',
            'unit_code_2' => 'nullable|string|max:255',
            'technician_1' => 'nullable|string|max:255',
            'technician_2' => 'nullable|string|max:255',
            'technician_3' => 'nullable|string|max:255',
            'technician_4' => 'nullable|string|max:255',
            'hm' => 'nullable|string|max:255',
            'problem_component' => 'nullable|string|max:255',
            'lifetime_component' => 'nullable|string|max:255',
            'date_in' => 'nullable|string|max:255',
            'problem_description' => 'nullable|string',
            'component_failure' => 'nullable|string',
            'plan_fix' => 'nullable|string',
            'plan_rekomen' => 'nullable|string',
            'part_problems' => 'nullable|array',
            'part_problems.*.part_name' => 'nullable|string|max:255',
            'part_problems.*.code_part' => 'nullable|string|max:255',
            'part_problems.*.quantity' => 'nullable|string|max:255',
            'part_problems.*.remarks' => 'nullable|string|max:255',
        ], [
            'unit_code.required' => 'Unit Code harus diisi.',
            'unit_code.max' => 'Unit Code maksimal 255 karakter.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors()
            ], 422);
        }

        // Prepare data for PDF generation
        $tarData = [
            'unit_code' => $request->input('unit_code', ''),
            'unit_code_2' => $request->input('unit_code_2', $request->input('unit_code', '')),
            'technician_1' => $request->input('technician_1', ''),
            'technician_2' => $request->input('technician_2', ''),
            'technician_3' => $request->input('technician_3', ''),
            'technician_4' => $request->input('technician_4', ''),
            'hm' => $request->input('hm', ''),
            'problem_component' => $request->input('problem_component', ''),
            'lifetime_component' => $request->input('lifetime_component', ''),
            'date_in' => $request->input('date_in', ''),
            'problem_description' => $request->input('problem_description', ''),
            'component_failure' => $request->input('component_failure', ''),
            'plan_fix' => $request->input('plan_fix', ''),
            'plan_rekomen' => $request->input('plan_rekomen', ''),
            'part_problems' => $request->input('part_problems', []),
        ];

        // Clean up part problems - remove empty entries
        if (isset($tarData['part_problems']) && is_array($tarData['part_problems'])) {
            $tarData['part_problems'] = array_filter($tarData['part_problems'], function($part) {
                return !empty($part['part_name']) || !empty($part['code_part']) ||
                       !empty($part['quantity']) || !empty($part['remarks']);
            });
        }

        try {
            // Generate PDF
            $pdf = Pdf::loadView('public-tar.pdf-template', compact('tarData'));
            $pdf->setPaper('a4', 'portrait');

            // Set PDF options for better rendering
            $pdf->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'dpi' => 150,
                'defaultPaperSize' => 'a4',
                'chroot' => public_path(),
            ]);

            // Generate filename with timestamp and unit code
            $unitCode = preg_replace('/[^A-Za-z0-9\-_]/', '_', $tarData['unit_code']);
            $filename = 'TAR_' . $unitCode . '_' . date('Y-m-d_H-i-s') . '.pdf';

            // Return PDF for download
            return $pdf->download($filename);

        } catch (\Exception $e) {
            Log::error('Public TAR PDF Generation Error: ' . $e->getMessage(), [
                'tarData' => $tarData,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error generating PDF: ' . $e->getMessage()
            ], 500);
        }
    }
}
